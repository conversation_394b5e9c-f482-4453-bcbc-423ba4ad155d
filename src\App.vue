<template>
  <div id="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>天气预报查询</span>
        </div>
      </template>
      <div class="controls">
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择一个日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
        <el-button type="primary" @click="fetchForecast" :loading="loading">查询</el-button>
      </div>
      <el-table :data="forecastData" v-loading="loading" style="width: 100%" class="forecast-table">
        <el-table-column prop="date" label="日期" />
        <el-table-column prop="weather" label="天气" />
        <el-table-column prop="temperature" label="温度" />
        <el-table-column prop="wind" label="风力" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';

const selectedDate = ref(null);
const forecastData = ref([]);
const loading = ref(false);

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = ('0' + (d.getMonth() + 1)).slice(-2);
  const day = ('0' + d.getDate()).slice(-2);
  return `${year}-${month}-${day}`;
};

const fetchForecast = async () => {
  loading.value = true;
  try {
    let queryDate = selectedDate.value;
    if (!queryDate) {
      queryDate = formatDate(new Date());
    }

    // 注意：这里的URL '/api/forecast' 假设你已经配置了Vite代理
    // 将/api请求转发到你的Java后端 (例如 http://localhost:8080)
    const response = await axios.post(`/api/forecast?date=${queryDate}`);

    if (response.data.code === 200) {
      // 假设后端返回的数据结构是 { code, message, data: [...] }
      // 并且 data 是一个数组，其中每个对象包含 date, weather, temperature, wind
      forecastData.value = response.data.data;
    } else {
      console.error('获取数据失败:', response.data.message);
      forecastData.value = [];
    }
  } catch (error) {
    console.error('请求错误:', error);
    forecastData.value = [];
  } finally {
    loading.value = false;
  }
};

// 组件加载时获取当天天气
onMounted(() => {
  selectedDate.value = formatDate(new Date());
  fetchForecast();
});
</script>

<style scoped>
#app-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 50px;
  min-height: 100vh;
  background-color: #f0f2f5;
}

.box-card {
  width: 800px;
}

.card-header {
  font-size: 20px;
  font-weight: bold;
  text-align: center;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.forecast-table {
  margin-top: 20px;
}
</style>
